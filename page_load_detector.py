# -*- coding: utf-8 -*-
"""
页面加载状态检测模块
使用OCR技术检测股票页面是否存在加载提示文字，判断页面是否完全加载
"""

import time
import logging
import mss
import numpy as np
import cv2
from typing import Optional, List, Dict, Any
from PIL import Image
from config import COMPASS_SOFTWARE, APP_CONFIG, PAGE_LOAD_DETECTION_CONFIG


class PageLoadDetector:
    """页面加载状态检测器"""
    
    def __init__(self, ocr_manager):
        """
        初始化页面加载检测器
        
        Args:
            ocr_manager: OCR管理器实例
        """
        self.logger = logging.getLogger(__name__)
        self.ocr_manager = ocr_manager
        
        # 从配置获取检测参数
        self.load_detection_config()
        
        self.logger.info(f"页面加载检测器初始化完成，检测区域: {self.detection_region}")
    
    def load_detection_config(self):
        """加载检测配置"""
        try:
            # 获取页面加载检测配置
            page_load_config = APP_CONFIG.get('page_load_detection', PAGE_LOAD_DETECTION_CONFIG)

            # 检测区域配置
            region_config = page_load_config.get('detection_region', {})

            if region_config.get('use_signal_region_as_base', True):
                # 使用买入信号区域作为基准
                buy_signal_region = COMPASS_SOFTWARE.get('buy_signal_region', {})

                if not buy_signal_region:
                    self.logger.warning("未找到买入信号区域配置，使用自定义区域")
                    custom_region = region_config.get('custom_region', {})
                    self.detection_region = {
                        'x': custom_region.get('x', 351),
                        'y': custom_region.get('y', 125),
                        'width': custom_region.get('width', 1060),
                        'height': custom_region.get('height', 37)
                    }
                else:
                    # 计算检测区域：使用信号区域的xy坐标和高度，宽度按倍数扩大
                    base_x = buy_signal_region.get('x', 351)
                    base_y = buy_signal_region.get('y', 125)
                    base_width = buy_signal_region.get('width', 53)
                    base_height = buy_signal_region.get('height', 37)
                    width_multiplier = region_config.get('width_multiplier', 20)

                    self.detection_region = {
                        'x': base_x,
                        'y': base_y,
                        'width': base_width * width_multiplier,
                        'height': base_height
                    }
            else:
                # 使用自定义检测区域
                custom_region = region_config.get('custom_region', {})
                self.detection_region = {
                    'x': custom_region.get('x', 351),
                    'y': custom_region.get('y', 125),
                    'width': custom_region.get('width', 1060),
                    'height': custom_region.get('height', 37)
                }

            # 加载提示文字列表
            self.loading_texts = page_load_config.get('loading_texts', [
                '最新数据获取中，请稍等',
                '最新数据',
                '数据获取中',
                '请稍等',
                '加载中',
                '数据加载中',
                '正在获取最新数据',
                '正在加载',
                '请稍候'
            ])

            # 检测参数
            self.max_retries = page_load_config.get('max_retries', 5)
            self.check_interval = page_load_config.get('check_interval', 0.1)
            self.similarity_threshold = page_load_config.get('similarity_threshold', 0.3)
            self.default_timeout = page_load_config.get('default_timeout', 10.0)
            self.debug_mode = page_load_config.get('debug_mode', True)

            # 错误处理配置
            error_config = page_load_config.get('error_handling', {})
            self.screenshot_retry_count = error_config.get('screenshot_retry_count', 2)
            self.ocr_retry_count = error_config.get('ocr_retry_count', 2)
            self.skip_on_detection_failure = error_config.get('skip_on_detection_failure', False)
            self.continue_on_timeout = error_config.get('continue_on_timeout', True)

            # 性能优化配置
            performance_config = page_load_config.get('performance', {})
            self.fast_detection_mode = performance_config.get('fast_detection_mode', False)
            self.cache_ocr_results = performance_config.get('cache_ocr_results', False)
            self.use_simplified_matching = performance_config.get('use_simplified_matching', False)

            # 如果启用快速检测模式，调整参数
            if self.fast_detection_mode:
                self.max_retries = min(self.max_retries, 3)
                self.check_interval = max(self.check_interval, 0.2)

            self.logger.info(f"页面加载检测配置加载完成")
            self.logger.info(f"检测区域: {self.detection_region}")
            self.logger.info(f"监控文字数量: {len(self.loading_texts)}")
            self.logger.info(f"最大重试次数: {self.max_retries}, 检测间隔: {self.check_interval}秒")
            self.logger.info(f"相似度阈值: {self.similarity_threshold}, 默认超时: {self.default_timeout}秒")

        except Exception as e:
            self.logger.error(f"加载检测配置失败: {str(e)}")
            # 使用默认配置
            self._load_default_config()

    def _load_default_config(self):
        """加载默认配置"""
        self.logger.warning("使用默认页面加载检测配置")
        self.detection_region = {'x': 351, 'y': 125, 'width': 1060, 'height': 37}
        self.loading_texts = [
            '最新数据获取中，请稍等',
            '最新数据',
            '数据获取中',
            '请稍等',
            '加载中'
        ]
        self.max_retries = 5
        self.check_interval = 0.1
        self.similarity_threshold = 0.3
        self.default_timeout = 10.0
        self.debug_mode = True
        self.screenshot_retry_count = 2
        self.ocr_retry_count = 2
        self.skip_on_detection_failure = False
        self.continue_on_timeout = True
        self.fast_detection_mode = False
        self.cache_ocr_results = False
        self.use_simplified_matching = False

    def is_page_loaded(self) -> bool:
        """
        检测页面是否已完全加载

        Returns:
            True: 页面已加载完成（未检测到加载文字）
            False: 页面仍在加载中（检测到加载文字）或检测失败
        """
        try:
            if self.debug_mode:
                self.logger.info("开始检测页面加载状态")
            else:
                self.logger.debug("开始检测页面加载状态")

            consecutive_failures = 0  # 连续失败次数

            for retry in range(self.max_retries):
                try:
                    # 截取检测区域（带重试）
                    screenshot = self._capture_detection_region_with_retry()
                    if screenshot is None:
                        consecutive_failures += 1
                        if self.debug_mode:
                            self.logger.warning(f"第{retry+1}次截图失败，连续失败{consecutive_failures}次")

                        # 如果连续失败次数过多且配置为不继续，则快速失败
                        if consecutive_failures >= 3 and not self.continue_on_timeout:
                            self.logger.warning(f"连续{consecutive_failures}次截图失败，快速返回检测失败")
                            return False

                        time.sleep(self.check_interval)
                        continue

                    # 重置连续失败计数
                    consecutive_failures = 0

                    # OCR识别文字（带重试）
                    detected_texts = self._perform_ocr_with_retry(screenshot)
                    if not detected_texts:
                        if self.debug_mode:
                            self.logger.info(f"第{retry+1}次OCR未识别到任何文字，页面已加载完成")
                        else:
                            self.logger.debug(f"第{retry+1}次OCR未识别到任何文字，页面已加载完成")
                        return True

                    # 检查是否包含加载提示文字
                    has_loading_text = self._check_loading_texts(detected_texts)

                    if has_loading_text:
                        if self.debug_mode:
                            self.logger.info(f"第{retry+1}次检测到加载文字，页面仍在加载中")
                        else:
                            self.logger.debug(f"第{retry+1}次检测到加载文字，页面仍在加载中")
                        time.sleep(self.check_interval)
                        continue
                    else:
                        if self.debug_mode:
                            self.logger.info(f"第{retry+1}次未检测到加载文字，页面已加载完成")
                        else:
                            self.logger.debug(f"第{retry+1}次未检测到加载文字，页面已加载完成")
                        return True

                except Exception as e:
                    consecutive_failures += 1
                    self.logger.warning(f"第{retry+1}次检测异常: {str(e)}，连续失败{consecutive_failures}次")

                    # 如果连续异常且配置为不继续，则快速失败
                    if consecutive_failures >= 3 and not self.continue_on_timeout:
                        self.logger.warning(f"连续{consecutive_failures}次检测异常，快速返回检测失败")
                        return False

                    time.sleep(self.check_interval)
                    continue

            # 所有重试都完成，根据配置决定返回值
            if self.continue_on_timeout:
                self.logger.info(f"完成{self.max_retries}次检测，未明确检测到加载文字，认为页面已加载")
                return True
            else:
                self.logger.warning(f"完成{self.max_retries}次检测，检测失败")
                return False

        except Exception as e:
            self.logger.error(f"页面加载检测失败: {str(e)}")
            # 根据配置决定异常时的行为
            if self.skip_on_detection_failure or not self.continue_on_timeout:
                return False
            else:
                # 出现异常时，为了安全起见，认为页面已加载（避免无限等待）
                return True
    
    def wait_for_page_load(self, timeout: float = 10.0) -> bool:
        """
        等待页面加载完成

        Args:
            timeout: 超时时间（秒）

        Returns:
            True: 页面加载完成
            False: 超时或检测失败
        """
        start_time = time.time()
        detection_count = 0

        self.logger.info(f"开始等待页面加载完成，超时时间: {timeout}秒")

        while time.time() - start_time < timeout:
            detection_count += 1
            page_loaded = self.is_page_loaded()

            if page_loaded:
                elapsed_time = time.time() - start_time
                self.logger.info(f"页面加载完成，耗时: {elapsed_time:.2f}秒，检测次数: {detection_count}")
                return True

            # 如果配置为不在超时时继续，且已经完成了一轮完整的检测
            # 则立即返回失败，避免继续等待
            if not self.continue_on_timeout and detection_count >= 1:
                elapsed_time = time.time() - start_time
                self.logger.warning(f"页面加载检测失败，快速返回，耗时: {elapsed_time:.2f}秒，检测次数: {detection_count}")
                return False

            # 短暂等待后继续检测
            time.sleep(0.2)

        # 超时
        elapsed_time = time.time() - start_time
        self.logger.warning(f"等待页面加载超时，耗时: {elapsed_time:.2f}秒，检测次数: {detection_count}")
        return self.continue_on_timeout
    
    def wait_for_page_load_with_debug(self, timeout: float = 10.0, message_queue=None) -> bool:
        """
        等待页面加载完成（带调试输出）
        
        Args:
            timeout: 超时时间（秒）
            message_queue: 消息队列，用于输出调试信息到GUI
            
        Returns:
            True: 页面加载完成
            False: 超时或检测失败
        """
        def log_debug(msg):
            """输出调试消息"""
            if message_queue:
                message_queue.put(("log", msg))
            self.logger.info(msg)
        
        start_time = time.time()
        check_count = 0
        
        log_debug(f"⏱️ 开始等待页面加载完成（超时: {timeout}秒）")
        
        while time.time() - start_time < timeout:
            check_count += 1
            elapsed = time.time() - start_time
            
            log_debug(f"🔄 第{check_count}次检查 (已等待{elapsed:.1f}秒)")
            
            try:
                # 截取检测区域
                screenshot = self._capture_detection_region()
                if screenshot is None:
                    log_debug("    ❌ 截图失败")
                    time.sleep(self.check_interval)
                    continue
                
                # OCR识别
                detected_texts = self._perform_ocr_with_debug(screenshot, message_queue)
                
                if detected_texts:
                    # 检查加载文字
                    combined_text = ' '.join(detected_texts).replace(' ', '')
                    log_debug(f"    🔍 合并文字: '{combined_text}'")
                    
                    # 检查是否包含加载文字
                    has_loading_text = self._check_loading_texts_with_debug(detected_texts, message_queue)
                    
                    if not has_loading_text:
                        log_debug("    ✅ 未发现加载文字，页面已加载完成")
                        elapsed_time = time.time() - start_time
                        log_debug(f"🎉 页面加载完成，耗时: {elapsed_time:.2f}秒，共检查{check_count}次")
                        return True
                    else:
                        log_debug("    ⏳ 检测到加载文字，继续等待...")
                else:
                    log_debug("    ✅ 未识别到文字，页面已加载完成")
                    elapsed_time = time.time() - start_time
                    log_debug(f"🎉 页面加载完成，耗时: {elapsed_time:.2f}秒，共检查{check_count}次")
                    return True
                    
            except Exception as e:
                log_debug(f"    ❌ 检查异常: {str(e)}")
            
            time.sleep(self.check_interval)
        
        # 超时
        elapsed_time = time.time() - start_time
        log_debug(f"⏰ 等待超时 ({timeout}秒)，共检查{check_count}次")
        return self.continue_on_timeout

    def _capture_detection_region_with_retry(self) -> Optional[np.ndarray]:
        """
        截取检测区域（带重试）

        Returns:
            截图图像数组或None
        """
        for retry in range(self.screenshot_retry_count + 1):
            try:
                screenshot = self._capture_detection_region()
                if screenshot is not None:
                    return screenshot

                if retry < self.screenshot_retry_count:
                    self.logger.debug(f"截图失败，第{retry+1}次重试...")
                    time.sleep(0.1)

            except Exception as e:
                if retry < self.screenshot_retry_count:
                    self.logger.debug(f"截图异常，第{retry+1}次重试: {str(e)}")
                    time.sleep(0.1)
                else:
                    self.logger.error(f"截图重试失败: {str(e)}")

        return None

    def _perform_ocr_with_retry(self, image: np.ndarray) -> List[str]:
        """
        对图像执行OCR识别（带重试）

        Args:
            image: 图像数组

        Returns:
            识别到的文字列表
        """
        for retry in range(self.ocr_retry_count + 1):
            try:
                texts = self._perform_ocr(image)
                if texts or retry == self.ocr_retry_count:
                    return texts

                if retry < self.ocr_retry_count:
                    self.logger.debug(f"OCR识别无结果，第{retry+1}次重试...")
                    time.sleep(0.1)

            except Exception as e:
                if retry < self.ocr_retry_count:
                    self.logger.debug(f"OCR识别异常，第{retry+1}次重试: {str(e)}")
                    time.sleep(0.1)
                else:
                    self.logger.error(f"OCR识别重试失败: {str(e)}")

        return []

    def _capture_detection_region(self) -> Optional[np.ndarray]:
        """
        截取检测区域
        
        Returns:
            截图图像数组或None
        """
        try:
            with mss.mss() as sct:
                # 构建截图区域
                region = {
                    'left': self.detection_region['x'],
                    'top': self.detection_region['y'],
                    'width': self.detection_region['width'],
                    'height': self.detection_region['height']
                }
                
                # 截取指定区域
                screenshot = sct.grab(region)
                
                # 转换为numpy数组
                img_array = np.array(screenshot)
                
                # 转换颜色格式 (BGRA -> BGR)
                if img_array.shape[2] == 4:
                    img_array = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)
                
                self.logger.debug(f"截取检测区域成功，尺寸: {img_array.shape}")
                return img_array
                
        except Exception as e:
            self.logger.error(f"截取检测区域失败: {str(e)}")
            return None
    
    def _perform_ocr(self, image: np.ndarray) -> List[str]:
        """
        对图像执行OCR识别
        
        Args:
            image: 图像数组
            
        Returns:
            识别到的文字列表
        """
        try:
            if not self.ocr_manager.is_initialized():
                self.logger.error("OCR管理器未初始化")
                return []
            
            # 获取OCR引擎
            ocr_engine = self.ocr_manager.get_ocr_engine()
            if ocr_engine is None:
                self.logger.error("无法获取OCR引擎")
                return []
            
            # 执行OCR识别
            if hasattr(ocr_engine, 'simple_engine') and hasattr(ocr_engine.simple_engine, 'recognize_image'):
                # 使用简化PaddleOCR引擎
                results = ocr_engine.simple_engine.recognize_image(image)
            elif hasattr(ocr_engine, 'recognize_image'):
                # 直接使用OCR引擎
                results = ocr_engine.recognize_image(image)
            else:
                self.logger.warning("OCR引擎不支持图像识别")
                return []
            
            # 提取文字内容
            texts = []
            if isinstance(results, list):
                for result in results:
                    if isinstance(result, str) and result.strip():
                        texts.append(result.strip())
            
            # 检查是否启用调试模式
            from config import APP_CONFIG
            debug_mode = APP_CONFIG.get('page_load_detection', {}).get('debug_mode', False)
            
            if debug_mode:
                if texts:
                    self.logger.info(f"页面加载检测OCR识别结果: {texts}")
                    combined_text = ' '.join(texts).replace(' ', '')
                    self.logger.info(f"页面加载检测合并文字: '{combined_text}'")
                else:
                    self.logger.info("页面加载检测OCR未识别到任何文字")
            else:
                self.logger.debug(f"OCR识别结果: {texts}")
            
            return texts
            
        except Exception as e:
            self.logger.error(f"OCR识别失败: {str(e)}")
            return []
    
    def _perform_ocr_with_debug(self, image: np.ndarray, message_queue=None) -> List[str]:
        """
        对图像执行OCR识别（带调试输出）
        
        Args:
            image: 图像数组
            message_queue: 消息队列，用于输出调试信息到GUI
            
        Returns:
            识别到的文字列表
        """
        def log_debug(msg):
            """输出调试消息"""
            if message_queue:
                message_queue.put(("log", msg))
            self.logger.info(msg)
            
        try:
            if not self.ocr_manager.is_initialized():
                log_debug("    ❌ OCR管理器未初始化")
                return []
            
            # 获取OCR引擎
            ocr_engine = self.ocr_manager.get_ocr_engine()
            if ocr_engine is None:
                log_debug("    ❌ 无法获取OCR引擎")
                return []
            
            # 执行OCR识别
            if hasattr(ocr_engine, 'simple_engine') and hasattr(ocr_engine.simple_engine, 'recognize_image'):
                # 使用简化PaddleOCR引擎
                results = ocr_engine.simple_engine.recognize_image(image)
            elif hasattr(ocr_engine, 'recognize_image'):
                # 直接使用OCR引擎
                results = ocr_engine.recognize_image(image)
            else:
                log_debug("    ❌ OCR引擎不支持图像识别")
                return []
            
            # 提取文字内容
            texts = []
            if isinstance(results, list):
                for result in results:
                    if isinstance(result, str) and result.strip():
                        texts.append(result.strip())
            
            # 输出调试信息
            if texts:
                log_debug(f"    📄 OCR识别结果: {texts}")
            else:
                log_debug("    📄 OCR未识别到任何文字")
            
            return texts
            
        except Exception as e:
            error_msg = f"    ❌ OCR识别失败: {str(e)}"
            log_debug(error_msg)
            return []
    
    def _check_loading_texts(self, detected_texts: List[str]) -> bool:
        """
        检查识别的文字中是否包含加载提示
        
        Args:
            detected_texts: 识别到的文字列表
            
        Returns:
            True: 包含加载提示文字
            False: 不包含加载提示文字
        """
        if not detected_texts:
            return False
        
        # 将所有识别文字合并为一个字符串
        combined_text = ' '.join(detected_texts).replace(' ', '')
        
        # 检查是否启用调试模式
        from config import APP_CONFIG
        debug_mode = APP_CONFIG.get('page_load_detection', {}).get('debug_mode', False)
        
        if debug_mode:
            self.logger.info(f"页面加载检测检查文字内容: '{combined_text}'")
            self.logger.info("页面加载检测相似度匹配过程:")
        else:
            self.logger.debug(f"检查文字内容: '{combined_text}'")
        
        # 检查是否包含任何加载提示文字
        max_similarity = 0.0
        best_match = ""
        found_loading_text = False
        
        for loading_text in self.loading_texts:
            similarity = self._text_similarity(combined_text, loading_text)
            if similarity > max_similarity:
                max_similarity = similarity
                best_match = loading_text
                
            if debug_mode:
                status = "✓ 匹配" if similarity >= self.similarity_threshold else "✗ 不匹配"
                self.logger.info(f"  '{loading_text}': {similarity:.2f} {status}")
            
            if similarity >= self.similarity_threshold:
                if not debug_mode:
                    self.logger.debug(f"检测到加载文字: '{loading_text}' (相似度: {similarity:.2f})")
                found_loading_text = True
        
        if debug_mode:
            if found_loading_text:
                self.logger.info(f"页面加载检测结果: 检测到加载文字 (最佳匹配: '{best_match}', 相似度: {max_similarity:.2f})")
            else:
                self.logger.info(f"页面加载检测结果: 未检测到加载文字 (最佳匹配: '{best_match}', 相似度: {max_similarity:.2f})")
        
        return found_loading_text
    
    def _check_loading_texts_with_debug(self, detected_texts: List[str], message_queue=None) -> bool:
        """
        检查识别的文字中是否包含加载提示（带调试输出）
        
        Args:
            detected_texts: 识别到的文字列表
            message_queue: 消息队列，用于输出调试信息到GUI
            
        Returns:
            True: 包含加载提示文字
            False: 不包含加载提示文字
        """
        def log_debug(msg):
            """输出调试消息"""
            if message_queue:
                message_queue.put(("log", msg))
            self.logger.info(msg)
            
        if not detected_texts:
            return False
        
        # 将所有识别文字合并为一个字符串
        combined_text = ' '.join(detected_texts).replace(' ', '')
        
        log_debug(f"    📊 相似度匹配过程:")
        
        # 检查是否包含任何加载提示文字
        max_similarity = 0.0
        best_match = ""
        found_loading_text = False
        
        for loading_text in self.loading_texts:
            similarity = self._text_similarity(combined_text, loading_text)
            if similarity > max_similarity:
                max_similarity = similarity
                best_match = loading_text
                
            status = "✓ 匹配" if similarity >= self.similarity_threshold else "✗ 不匹配"
            log_debug(f"      '{loading_text}': {similarity:.2f} {status}")
            
            if similarity >= self.similarity_threshold:
                found_loading_text = True
        
        if found_loading_text:
            log_debug(f"    🎯 检测结果: 发现加载文字 (最佳匹配: '{best_match}', 相似度: {max_similarity:.2f})")
        else:
            log_debug(f"    🎯 检测结果: 未发现加载文字 (最佳匹配: '{best_match}', 相似度: {max_similarity:.2f})")
        
        return found_loading_text
    
    def _text_similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文字的相似度
        
        Args:
            text1: 文字1
            text2: 文字2
            
        Returns:
            相似度 (0.0-1.0)
        """
        if not text1 or not text2:
            return 0.0
        
        # 简单的包含检查
        if text2 in text1 or text1 in text2:
            return 1.0
        
        # 字符级别的相似度检查
        common_chars = set(text1) & set(text2)
        total_chars = set(text1) | set(text2)
        
        if not total_chars:
            return 0.0
        
        return len(common_chars) / len(total_chars)
    
    def get_detection_status(self) -> Dict[str, Any]:
        """
        获取检测器状态信息
        
        Returns:
            状态信息字典
        """
        return {
            'detection_region': self.detection_region,
            'loading_texts': self.loading_texts,
            'max_retries': self.max_retries,
            'check_interval': self.check_interval,
            'similarity_threshold': self.similarity_threshold,
            'default_timeout': self.default_timeout,
            'debug_mode': self.debug_mode,
            'fast_detection_mode': self.fast_detection_mode,
            'continue_on_timeout': self.continue_on_timeout,
            'skip_on_detection_failure': self.skip_on_detection_failure,
            'ocr_initialized': self.ocr_manager.is_initialized() if self.ocr_manager else False
        }

    def update_config(self, **kwargs):
        """
        动态更新检测器配置

        Args:
            **kwargs: 配置参数
        """
        for key, value in kwargs.items():
            if hasattr(self, key):
                old_value = getattr(self, key)
                setattr(self, key, value)
                self.logger.info(f"更新配置 {key}: {old_value} -> {value}")
            else:
                self.logger.warning(f"未知配置参数: {key}")

    def get_loading_text_count(self) -> int:
        """获取加载文字数量"""
        return len(self.loading_texts)

    def add_loading_text(self, text: str):
        """添加新的加载文字"""
        if text not in self.loading_texts:
            self.loading_texts.append(text)
            self.logger.info(f"添加新的加载文字: '{text}'")
        else:
            self.logger.debug(f"加载文字已存在: '{text}'")

    def remove_loading_text(self, text: str):
        """移除加载文字"""
        if text in self.loading_texts:
            self.loading_texts.remove(text)
            self.logger.info(f"移除加载文字: '{text}'")
        else:
            self.logger.debug(f"加载文字不存在: '{text}'")


# 全局便捷函数
def create_page_load_detector(ocr_manager) -> PageLoadDetector:
    """
    创建页面加载检测器的便捷函数

    Args:
        ocr_manager: OCR管理器实例

    Returns:
        PageLoadDetector实例
    """
    return PageLoadDetector(ocr_manager)


def check_page_loading_status(ocr_manager, timeout: float = None) -> bool:
    """
    检查页面加载状态的便捷函数

    Args:
        ocr_manager: OCR管理器实例
        timeout: 超时时间，None表示使用默认值

    Returns:
        True: 页面已加载完成
        False: 页面仍在加载或检测失败
    """
    try:
        # 检查是否启用页面加载检测
        page_load_config = APP_CONFIG.get('page_load_detection', PAGE_LOAD_DETECTION_CONFIG)
        if not page_load_config.get('enabled', True):
            return True  # 如果禁用检测，直接返回True

        # 创建检测器
        detector = PageLoadDetector(ocr_manager)

        # 使用指定的超时时间或默认值
        if timeout is None:
            timeout = detector.default_timeout

        # 执行检测
        return detector.wait_for_page_load(timeout)

    except Exception as e:
        logging.getLogger(__name__).error(f"页面加载状态检查失败: {str(e)}")
        return True  # 出错时返回True，避免阻塞流程


def is_page_load_detection_enabled() -> bool:
    """
    检查页面加载检测是否启用

    Returns:
        True: 启用
        False: 禁用
    """
    page_load_config = APP_CONFIG.get('page_load_detection', PAGE_LOAD_DETECTION_CONFIG)
    return page_load_config.get('enabled', True)
